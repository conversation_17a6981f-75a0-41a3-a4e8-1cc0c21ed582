services:
  # Main application service
  app:
    build: .
    container_name: carbon-regulation-news
    ports:
      - "8000:8000"
    environment:
      # Application settings
      - ENVIRONMENT=production
      - DEBUG=false
      - LOG_LEVEL=INFO

      # Database configuration (using SQLite for simplicity)
      - DATABASE__URL=sqlite:///./data/carbon_news.db
      - DATABASE__ECHO=false

      # API server settings
      - API__HOST=0.0.0.0
      - API__PORT=8000
      - API__DEBUG=false

      # News collection settings
      - NEWS_COLLECTOR__MAX_ARTICLES_PER_SOURCE=10
      - NEWS_COLLECTOR__DEFAULT_TIME_RANGE=day

      # Scheduler settings
      - SCHEDULER__DAILY_RUN_TIME=09:00
      - SCHEDULER__MAX_TASK_HISTORY=100
      - SCHEDULER__TASK_TIMEOUT_MINUTES=30

      # Notification settings
      - NOTIFICATIONS__ENABLE_NOTIFICATIONS=true

      # API Keys (set these in .env file)
      - TAVILY_API_KEY=${TAVILY_API_KEY}
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}

    volumes:
      # Persist database and logs
      - ./data:/app/data
      - ./logs:/app/logs

    restart: unless-stopped

    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

    # Ensure data and logs directories exist
    depends_on: []
